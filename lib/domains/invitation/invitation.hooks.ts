"use client"

import { use<PERSON>ffe<PERSON>, useState, useMemo } from "react"
import { useInvitationStore } from "./invitation.store"
import {
  InvitationLink,
  InvitationLinkCreateData,
  InvitationSend,
  InvitationSendCreateData,
} from "./invitation.types"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { generateInvitationLink, sendInvitationEmail } from "@/lib/email-service"
import { SquadService } from "../squad/squad.service"
import { UserService } from "../user/user.service"
import { InvitationService } from "./invitation.service"
import { InvitationSendService } from "./invitation-send.service"

// Export real-time hooks
export * from "./invitation.realtime.hooks"

// Legacy hooks removed - these are no longer used in the current invitation system
// The modern invitation system uses:
// - useSendInvitationEmails for sending invitations
// - useInvitationLink for managing invitation links
// - useSquadInvitation for invitation page functionality

// useInviteUserToSquad hook removed - only used in legacy shared dialog
// Modern invitation flow uses useSendInvitationEmails with invitation links

// ===== NEW INVITATION LINK HOOKS =====

/**
 * Hook to get or create an invitation link for a squad
 * Uses the invitation store for shared state across components
 */
export const useInvitationLink = (squadId: string) => {
  const user = useUser()
  const {
    invitationLinks,
    loading,
    error,
    fetchInvitationLink: storeFetchInvitationLink,
  } = useInvitationStore()

  // Memoize dependencies to prevent infinite re-renders
  const memoizedSquadId = useMemo(() => squadId, [squadId])

  // Get the invitation link for this specific squad from the store
  const invitationLink = useMemo(() => {
    return invitationLinks[memoizedSquadId] || null
  }, [invitationLinks, memoizedSquadId])

  useEffect(() => {
    if (memoizedSquadId && user) {
      storeFetchInvitationLink(memoizedSquadId)
    }
  }, [memoizedSquadId, user, storeFetchInvitationLink])

  // Expose refetch function for manual updates
  const refetch = () => {
    if (memoizedSquadId) {
      storeFetchInvitationLink(memoizedSquadId)
    }
  }

  return { invitationLink, loading, error, refetch }
}

/**
 * Hook to generate a new invitation link
 */
export const useGenerateInvitationLink = (onSuccess?: (invitationLink: InvitationLink) => void) => {
  const user = useUser()
  const { setInvitationLink } = useInvitationStore()
  const [generating, setGenerating] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  const generate = async (squadId: string, squadName: string) => {
    if (!user) {
      setError(new Error("User not authenticated"))
      return null
    }

    try {
      setGenerating(true)
      setError(null)

      // Check if there's already an active invitation link
      const existingLink = await InvitationService.getActiveInvitationLink(squadId)
      if (existingLink) {
        setGenerating(false)
        // Update the store with the existing link
        setInvitationLink(squadId, existingLink)
        // Call success callback even for existing links
        onSuccess?.(existingLink)
        return existingLink
      }

      // Create new invitation link
      const invitationLinkData: InvitationLinkCreateData = {
        squadId,
        squadName,
        inviterId: user.uid,
        inviterName: user.displayName || user.email || "",
      }

      const invitationLinkId = await InvitationService.createInvitationLink(invitationLinkData)
      const newInvitationLink = await InvitationService.getInvitationLink(invitationLinkId)

      setGenerating(false)

      if (newInvitationLink) {
        // Update the store with the new invitation link
        setInvitationLink(squadId, newInvitationLink)
        // Call success callback to trigger refetch
        onSuccess?.(newInvitationLink)
      }

      return newInvitationLink
    } catch (err) {
      console.error("Error generating invitation link:", err)
      setError(err as Error)
      setGenerating(false)
      return null
    }
  }

  return { generate, generating, error }
}

/**
 * Hook to send individual email invitations
 * Implements comprehensive invitation logic with proper validation rules:
 * - Rejected: ❌ No, rejected: ✅ YES (User can change mind)
 * - Accepted: ❌ No, accepted: ✅ YES (Edge case recovery)
 * - Pending: ❌ No, sent (not expired): ❌ NO (Prevents spam)
 * - Pending: ❌ No, sent (expired): ✅ YES (Expired invitation)
 * - Any: ✅ Yes, Any: ❌ NO (Already member)
 */
export const useSendInvitationEmails = () => {
  const user = useUser()
  const [sending, setSending] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  const sendEmails = async (squadId: string, invitationId: string, emails: string[]) => {
    if (!user) {
      setError(new Error("User not authenticated"))
      return { success: false, error: "User not authenticated" }
    }

    try {
      setSending(true)
      setError(null)

      const results = []

      for (const email of emails) {
        // COMPREHENSIVE INVITATION VALIDATION LOGIC

        // 1. Check if user is already a squad member
        const invitee = await UserService.getUserByEmail(email)
        if (invitee) {
          const isAlreadyMember = await SquadService.isUserSquadMember(invitee.uid, squadId)
          if (isAlreadyMember) {
            results.push({
              email,
              success: false,
              error: "User is already a member of this squad",
            })
            continue
          }
        }

        // 2. Get existing invitation sends for this squad to check status and expiration
        const existingInvitationSends = await InvitationSendService.getSquadInvitationSends(squadId)
        const userInvitationSends = existingInvitationSends.filter(
          (send) => send.email.toLowerCase() === email.toLowerCase()
        )

        // 3. Check for pending (sent) invitations that are not expired
        const pendingInvitationSend = userInvitationSends.find((send) => {
          if (send.status !== "sent") return false

          // Check if invitation is expired (7 days from sentAt)
          const isExpired = InvitationSendService.isInvitationSendExpired(send, 7)
          return !isExpired // Return true if NOT expired (still pending)
        })

        if (pendingInvitationSend) {
          results.push({
            email,
            success: false,
            error: "This email already has a pending invitation that hasn't expired",
          })
          continue
        }

        // Note: We don't check hasEmailBeenSent here because our comprehensive validation above
        // already handles all re-invitation scenarios properly based on status and expiration

        // Create invitation send record first
        const invitationSendData: InvitationSendCreateData = {
          invitationId,
          email: email.toLowerCase(),
        }

        try {
          const sendId = await InvitationSendService.createInvitationSend(
            squadId,
            invitationSendData
          )

          // Send the actual email with the specific invitation send ID
          const invitationLink = generateInvitationLink(invitationId, sendId)
          const emailResult = await sendInvitationEmail(
            {
              id: invitationId,
              inviteeEmail: email,
              // Add other required fields for email template
            } as any,
            invitationLink,
            undefined, // templateId
            sendId // Pass the invitation send ID
          )

          results.push({
            email,
            success: emailResult.success,
            error: emailResult.error,
            sendId,
          })
        } catch (err) {
          results.push({
            email,
            success: false,
            error: (err as Error).message,
          })
        }
      }

      setSending(false)
      return { success: true, results }
    } catch (err) {
      console.error("Error sending invitation emails:", err)
      setError(err as Error)
      setSending(false)
      return { success: false, error: (err as Error).message }
    }
  }

  return { sendEmails, sending, error }
}

/**
 * Hook to get invitation sends for a squad (non-realtime)
 */
export const useInvitationSends = (squadId: string, invitationId?: string) => {
  const [invitationSends, setInvitationSends] = useState<InvitationSend[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  // Memoize dependencies to prevent infinite re-renders
  const memoizedDependencies = useMemo(() => ({ squadId, invitationId }), [squadId, invitationId])

  useEffect(() => {
    if (!memoizedDependencies.squadId) {
      setInvitationSends([])
      setLoading(false)
      return () => {}
    }

    const fetchInvitationSends = async () => {
      try {
        setLoading(true)
        setError(null)

        let sends: InvitationSend[]
        if (memoizedDependencies.invitationId) {
          sends = await InvitationSendService.getInvitationSends(
            memoizedDependencies.squadId,
            memoizedDependencies.invitationId
          )
        } else {
          sends = await InvitationSendService.getSquadInvitationSends(memoizedDependencies.squadId)
        }

        setInvitationSends(sends)
        setLoading(false)
      } catch (err) {
        console.error("Error fetching invitation sends:", err)
        setError(err as Error)
        setLoading(false)
      }
    }

    fetchInvitationSends()

    return () => {}
  }, [memoizedDependencies])

  return { invitationSends, loading, error }
}
